# CREST PROJECT 配置文件参数详解

## 📋 目录
1. [基本信息](#基本信息)
2. [时间设置](#时间设置)
3. [模型开关](#模型开关)
4. [数据路径](#数据路径)
5. [强迫数据设置](#强迫数据设置)
6. [观测数据设置](#观测数据设置)
7. [输出设置](#输出设置)
8. [系统设置](#系统设置)

---

## 🔧 基本信息

### Version
```ini
Version = 2.1.3
```
- **含义**：CREST模型版本号
- **作用**：标识配置文件兼容的模型版本
- **建议**：不要修改，保持与软件版本一致

---

## ⏰ 时间设置

### TimeMark
```ini
TimeMark = d
```
- **含义**：时间单位标识
- **可选值**：
  - `y` = 年 (year)
  - `m` = 月 (month) 
  - `d` = 日 (day)
  - `h` = 小时 (hour)
  - `u` = 分钟 (minute)
  - `s` = 秒 (second)
- **建议**：水文模拟通常使用 `h`(小时) 或 `d`(日)

### TimeFormat
```ini
TimeFormat = yyyymmddHH
```
- **含义**：时间格式字符串
- **作用**：定义日期时间的表示格式
- **常用格式**：
  - `yyyymmddHH` = 2020081911 (年月日时)
  - `yyyymmdd` = 20200819 (年月日)
- **注意**：必须与数据文件的时间格式一致

### TimeStep
```ini
TimeStep = 1
```
- **含义**：时间步长
- **单位**：由TimeMark决定
- **示例**：
  - TimeMark=h, TimeStep=1 → 1小时步长
  - TimeMark=d, TimeStep=1 → 1天步长

### StartDate
```ini
StartDate = 2020081508
```
- **含义**：模拟开始日期
- **格式**：按TimeFormat格式
- **作用**：定义强迫数据读取的起始时间

### WarmupDate  
```ini
WarmupDate = 2020081911
```
- **含义**：暖启动结束日期
- **作用**：模型在此日期前进行预热，不计入结果统计
- **建议**：通常设置为StartDate后几天，让模型状态稳定

### EndDate
```ini
EndDate = 2020090205  
```
- **含义**：模拟结束日期
- **作用**：定义模拟的终止时间

### NLoad
```ini
NLoad = 0
```
- **含义**：状态变量加载次数/模式
- **详细说明**：
  - `0` = 常规模拟模式
    - 模型从默认初始状态开始
    - 不加载任何预设的状态变量文件
    - 适用于正常的连续模拟
  - `>0` = 状态加载模式
    - 在暖启动日期(WarmupDate)加载预设的状态变量
    - 数值表示加载状态变量的次数或批次
    - 用于断点续算、热启动或特定初始条件模拟
- **应用场景**：
  - **NLoad=0**：新模拟、参数测试、完整时间序列模拟
  - **NLoad>0**：实时预报、断点续算、敏感性分析
- **相关文件**：状态文件存储在StatePath指定的目录中

---

## 🔀 模型开关

### RunStyle
```ini
RunStyle = cali_SCEUA
```
- **含义**：运行模式
- **可选值**：
  - `simu` = 模拟模式（使用给定参数）
  - `cali_SCEUA` = SCE-UA参数优化模式
  - `RealTime` = 实时预报模式
- **建议**：新手先用simu模式测试

### Feedback
```ini
Feedback = No
```
- **含义**：汇流反馈开关
- **作用**：是否将汇流结果反馈给陆面模型
- **可选值**：`Yes` / `No`
- **建议**：一般设为No，减少计算复杂度

### hasRiverInterflow
```ini
hasRiverInterflow = No
```
- **含义**：河道壤中流开关
- **作用**：
  - `No` = 河道内所有壤中流转为地表径流
  - `Yes` = 保持河道壤中流独立计算
- **建议**：根据流域特征选择，山区流域可考虑Yes

### UseLAI
```ini
UseLAI = No
```
- **含义**：叶面积指数使用开关
- **作用**：是否使用LAI计算降雨截留
- **可选值**：`Yes` / `No`
- **注意**：设为Yes需要提供LAI数据

---

## 📁 数据路径

### 基础地理数据
```ini
BasicFormat = .tif
BasicPath = "D:\CREST\...\BASIC\"
```
- **BasicFormat**：基础数据文件格式
- **BasicPath**：基础地理数据目录路径
- **包含文件**：dem.tif, fdr.tif, fac.tif, stream.tif, mask.tif

### 模型参数
```ini
ParamFormat = .asc  
ParamPath = "D:\CREST\...\ParametersTest.txt"
```
- **ParamFormat**：参数文件格式
- **ParamPath**：参数文件完整路径
- **内容**：11个水文参数的空间分布

### 状态变量
```ini
StateFormat = .asc
StatePath = "D:\CREST\...\States\"
```
- **StateFormat**：状态文件格式
- **StatePath**：状态变量保存目录
- **用途**：保存模型运行中的状态变量

### 初始条件
```ini
ICSFormat = .asc
ICSPath = "D:\CREST\...\ICS\"
```
- **ICSFormat**：初始条件文件格式  
- **ICSPath**：初始条件文件目录
- **用途**：设置模型初始状态

---

## 🌧️ 强迫数据设置

### 降雨数据 (外部)
```ini
RainFormat = .24h.Z
RainDateFormat = yyyymmddHH
RainDateConv = End
RainStart = 2020081508
RainDateInterval = 0000000100
RainPathExt = "G:\mete data\StageIV_daily\ST4."
RainTsScaling = 1
```
- **RainFormat**：外部降雨文件格式后缀
- **RainDateFormat**：外部降雨文件日期格式
- **RainDateConv**：日期转换方式
  - `End` = 文件时间表示时段结束时刻
  - `Begin` = 文件时间表示时段开始时刻
  - `Center` = 文件时间表示时段中心时刻
- **RainStart**：外部降雨数据起始时间
- **RainDateInterval**：外部降雨数据时间间隔
  - 格式：yyyymmddHH，如0000000100表示1小时间隔
- **RainPathExt**：外部降雨数据路径前缀
- **RainTsScaling**：降雨数据缩放因子（通常为1）

### 降雨数据 (内部)
```ini
RainPathInt = "D:\CREST\...\rains_daily\rain."
```
- **RainPathInt**：内部降雨数据路径前缀
- **文件命名**：rain.yyyymmddHH.mat
- **变量名**：data (矩阵格式)

### PET数据 (外部)
```ini
PETFormat = .bil
PETDateFormat = yymmdd
PETDateConv = Begin
PETStart = 020101
PETDateInterval = 000001
PETPathExt = "G:\mete data\PET_FEWS\et"
PETTsScaling = 100
```
- **PETFormat**：外部PET文件格式后缀
- **PETDateFormat**：外部PET文件日期格式
- **PETDateConv**：PET数据日期转换方式
- **PETStart**：外部PET数据起始时间
- **PETDateInterval**：外部PET数据时间间隔
- **PETPathExt**：外部PET数据路径前缀
- **PETTsScaling**：PET数据缩放因子
  - 示例：FEWS数据有100倍缩放，实际值需除以100

### PET数据 (内部)
```ini
PETPathInt = "D:\CREST\...\PETs_daily\PET."
```
- **PETPathInt**：内部PET数据路径前缀
- **文件命名**：pet_yyyymmddHH.mat
- **变量名**：data (矩阵格式)

### LAI数据
```ini
LAIFormat = _mosaic.tif
LAIDateFormat = yyyy\DOY
LAIStart = 2006\001
LAIDateInterval = 0000\008
LAIPathExt = "C:\data\LAI\Mosaic\"
LAITsScaling = 10
LAIDateConv = Center
LAIPathInt = "D:\CREST\...\LAI_daily\"
```
- **仅在UseLAI=Yes时需要**

---

## 📊 观测数据设置

### 观测数据格式
```ini
OBSDateFormat = yyyymmddHH
OBSPath = "D:\CREST\...\obs\"
OBSNoDataValue = -9999
```
- **OBSDateFormat**：观测数据日期格式
- **OBSPath**：观测数据目录路径
- **OBSNoDataValue**：缺测值标识

### 出水口设置
```ini
HasOutlet = yes
OutletName = 110.68916600000
SitesShpFile = tp.shp
```
- **HasOutlet**：是否有出水口观测
- **OutletName**：出水口站点ID（必须与shapefile中一致）
- **SitesShpFile**：站点shapefile文件名

---

## 📈 输出设置

### 结果路径
```ini
ResultFormat = asc
ResultPath = "D:\CREST\...\result\"
```

### 优化设置
```ini
CalibFormat = asc
CalibPath = "D:\CREST\...\Calib\"
CalibMode = Parallel
```
- **CalibMode**：优化计算模式
  - `Parallel` = 并行计算（快速，需要工具箱）
  - `Sequential` = 串行计算（稳定，较慢）

### 栅格输出开关
```ini
GOVar_Rain = No      # 降雨栅格输出
GOVar_PET = No       # PET栅格输出  
GOVar_EAct = No      # 实际蒸散发栅格输出
GOVar_W = No         # 土壤含水量栅格输出
GOVar_SM = No        # 土壤湿度栅格输出
GOVar_R = No         # 径流栅格输出
GOVar_ExcS = No      # 地表超渗栅格输出
GOVar_ExcI = No      # 壤中流栅格输出
GOVar_RS = No        # 地表径流栅格输出
GOVar_RI = No        # 壤中径流栅格输出
```
- **作用**：控制是否输出空间分布栅格结果
- **建议**：大流域建议设为No，节省存储空间

### 输出时间设置
```ini
NumOfOutputDates = 0
SaveDateFormat = "yyyymmddHHMM"
DateOffset = 000000000030
OutputDate_1 = 2020081911
OutputDate_2 = 2020082014
OutputDate_3 = 2020082520  
OutputDate_4 = 2020090205
```
- **NumOfOutputDates**：指定输出日期的数量
- **SaveDateFormat**：保存文件的日期格式
- **DateOffset**：时间偏移量
- **OutputDate_X**：具体的输出时间点

---

## 🖥️ 系统设置

### 解压设置
```ini
DecompBeforeSrc = ""D:\CREST\...\MEX" -ibck x "
DecompBeforeDst = " "
OS = "windows"
```
- **DecompBeforeSrc**：解压命令源路径
- **DecompBeforeDst**：解压命令目标路径  
- **OS**：操作系统类型
- **用途**：处理压缩格式的强迫数据

---

## ⚠️ 重要注意事项

1. **路径格式**：Windows系统使用反斜杠 `\`
2. **路径引号**：包含空格的路径必须用双引号包围
3. **文件存在性**：确保所有路径指向的文件/目录存在
4. **时间一致性**：各时间参数必须逻辑一致
5. **数据格式**：强迫数据格式必须与设置匹配
6. **坐标系统**：所有空间数据必须使用相同坐标系

## 🎯 配置建议

**新手用户**：
- RunStyle = simu
- CalibMode = Sequential  
- 所有GOVar设为No
- 使用内部数据路径(PathInt)

**高级用户**：
- 根据需要开启各种输出选项
- 使用并行优化模式
- 配置外部数据源

**祝您配置成功！** 🎉
