###############################################################################
# CREST Project File
###############################################################################
Version						=			2.1.3
###############################################################################
# MODEL Run Time Information
###############################################################################
TimeMark					=			h	#y(year);m(month);d(day);h(hour);u(minute);s(second)
TimeFormat				=			yyyymmddHH
TimeStep					=			1
StartDate					= 		**********
NLoad					=			0 #Nload=0 regular simulation; NLoad>0, states variables are loaded at warmup dates
WarmupDate				=			**********
EndDate						= 		**********
###############################################################################
# MODEL Switchers
###############################################################################
RunStyle					=			simu # simu, cali_SCEUA, RealTime
Feedback					=			No 	# routing feeds the LSM back
hasRiverInterflow				=			No	#No: all interflow turns to surface flow in the river
UseLAI				=			No		# compute the rainfall interception
###############################################################################
# MODEL Directories
###############################################################################
BasicFormat				=			.tif #any gdal supported formats
BasicPath					=			"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\BASIC\"
###############################################################################
ParamFormat				=			.asc
ParamPath					=			"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\Param\ParametersTest.txt"
###############################################################################
StateFormat				=			.asc
StatePath					=			"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\States\"
###############################################################################
ICSFormat					=			.asc
ICSPath						=			"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\ICS\"
###############################################################################
# external rainfall settings 
RainFormat				=			.24h.Z
RainDateFormat				=			yyyymmddHH
RainDateConv				=				End
RainStart				=				**********
RainDateInterval				=				0000000100
RainPathExt					=			"G:\mete data\StageIV_daily\ST4."
RainTsScaling				=			1
# internal rainfall setting
RainPathInt				=				"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\Rains_daily\rain."
###############################################################################
PETFormat					=			.bil
PETDateFormat				=			yymmdd
PETDateConv				=				Begin
PETStart			=			020101
PETDateInterval			=			000001
PETPathExt						=			"G:\mete data\PET_FEWS\et"
PETTsScaling			=			1 # Adjusted scaling factor for local PET data
# internal PET setting
PETPathInt			=			"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\PETs_daily\pet_"
###############################################################################
LAIFormat					=			_mosaic.tif
LAIDateFormat				=			yyyy\DOY
LAIStart			=			2006\001
LAIDateInterval			=			0000\008
LAIPathExt						=			"C:\data\LAI\Mosaic\"
LAITsScaling			=			10 # LAI data has a 10 scaling factor and 255 is water body: http://glass-product.bnu.edu.cn/en
LAIDateConv				=				Center/d/file/wendangziliao/suanfawendang/2014-03-13/GLASS%20LAI%E4%BA%A7%E5%93%81%E7%94%A8%E6%88%B7%E4%BD%BF%E7%94%A8%E6%89%8B%E5%86%8C_v1.1.pdf
LAIPathInt						=			"G:\data\LAI\Mosaic\"
LAIPathInt						=			"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\LAI_daily\"
###############################################################################
ResultFormat			=			asc
ResultPath				=			"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\result\"
###############################################################################
CalibFormat				=			asc
CalibPath					=			"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\Calib\"
CalibMode					=			Sequential # Parallel
###############################################################################
OBSDateFormat					=			yyyymmddHH
OBSPath						=			"D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\obs\"
OBSNoDataValue			=				-9999
###############################################################################
# The below data are optional. when RunStyle=cali_SCEUA, they can be omited
###############################################################################
#Outlet Information
###############################################################################
HasOutlet					=			yes
OutletName				=			80607500	#tp station the filename of observation
SitesShpFile				=		80607500.shp  # the shapefile name
###############################################################################
#Grid Outputs
###############################################################################
GOVar_Rain				=			No
GOVar_PET					=			No
GOVar_EPot				=			No
GOVar_EAct				=			No
GOVar_W						=			No
GOVar_SM					=			No
GOVar_R						=			No
GOVar_ExcS				=			No
GOVar_ExcI				=			No
GOVar_RS					=			No
GOVar_RI					=			No
###############################################################################
NumOfOutputDates	=			7 #7
SaveDateFormat				=			"yyyymmddHHMM"
DateOffset				=			000000000030
OutputDate_1			=			**********
OutputDate_2			=			2020040320
OutputDate_3			=			2020040420
OutputDate_4			=			2020040520
OutputDate_5			=			2020040820
OutputDate_6			=			2020041120
OutputDate_7			=			2020041420
###############################################################################
DecompBeforeSrc				=				""D:\CREST\CREST2.1.3_redist\MJ2.1.3\MEX" -ibck x "
DecompBeforeDst 			=				" "
OS									=					"windows"
