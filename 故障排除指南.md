# CREST 模型故障排除指南

## 🔍 问题诊断流程

### 1. 基础检查清单
- [ ] MATLAB版本 ≥ R2014a
- [ ] Windows x64 系统
- [ ] 所有数据文件存在
- [ ] 路径中无中文字符
- [ ] 有足够磁盘空间 (>2GB)

### 2. 数据文件检查
```matlab
% 在MATLAB中运行以下代码检查数据
fprintf('检查基础数据文件...\n');
files = {'dem.tif', 'fdr.tif', 'fac.tif', 'stream.tif', 'mask.tif'};
for i = 1:length(files)
    file = ['example\Tar\basic\', files{i}];
    if exist(file, 'file')
        fprintf('✓ %s 存在\n', files{i});
    else
        fprintf('✗ %s 缺失\n', files{i});
    end
end
```

---

## ❌ 常见错误及解决方案

### 错误类型1：文件路径问题

**错误信息**：
```
Error: File not found
Cannot open file: xxx.tif
```

**解决方案**：
1. 检查配置文件中的路径格式：
   ```ini
   # 正确格式 (使用反斜杠)
   BasicPath = "D:\CREST\example\Tar\basic\"
   
   # 错误格式
   BasicPath = "D:/CREST/example/Tar/basic/"
   ```

2. 确保路径中无中文字符
3. 使用绝对路径而非相对路径

### 错误类型2：观测数据问题

**错误信息**：
```
Error: observation of outlets must be provided
Index exceeds array bounds
```

**解决方案**：
1. 检查观测文件命名：
   ```
   正确：110.68916600000_obs.csv
   错误：tp_obs.csv
   ```

2. 检查观测数据格式：
   ```csv
   Date,Flow
   **********,10.5
   2020081912,12.3
   ```

3. 确保日期格式与配置一致：
   ```ini
   OBSDateFormat = yyyymmddHH
   ```

### 错误类型3：坐标系统问题

**错误信息**：
```
Error: Outlet position is outside DEM bounds
Index exceeds matrix dimensions
```

**解决方案**：
1. 检查shapefile坐标系与DEM是否一致
2. 验证站点位置是否在流域范围内
3. 代码已自动修复此问题，使用流域中心作为备选位置

### 错误类型4：强迫数据问题

**错误信息**：
```
Error: Unrecognized field name 'srain'
Cannot load forcing data
```

**解决方案**：
1. 检查MAT文件中的变量名：
   ```matlab
   % 降雨文件应包含变量 'data'
   load('rain.**********.mat')
   whos  % 查看变量名
   ```

2. 确保文件命名格式正确：
   ```
   降雨：rain.yyyymmddHH.mat
   PET：pet_yyyymmddHH.mat
   ```

### 错误类型5：并行计算问题

**错误信息**：
```
Error: Parallel pool timeout
Job cancelled due to timeout
```

**解决方案**：
1. 检查Parallel Computing Toolbox：
   ```matlab
   license('test', 'Distrib_Computing_Toolbox')
   ```

2. 改用串行模式：
   ```ini
   CalibMode = Sequential
   ```

3. 减少优化参数：
   ```ini
   maxn = 1000      # 减少迭代次数
   ngs = 2          # 减少复合体数量
   ```

### 错误类型6：内存不足

**错误信息**：
```
Error: Out of memory
Not enough memory available
```

**解决方案**：
1. 关闭其他程序释放内存
2. 重启MATLAB清理内存：
   ```matlab
   clear all
   close all
   clc
   ```

3. 减少数据规模或使用更小的时间窗口

---

## 🔧 高级故障排除

### 调试模式运行
```matlab
% 开启详细错误信息
dbstop if error

% 运行模型
cd('CREST_app')
CREST('配置文件路径', 'auto', 1)
```

### 检查中间结果
```matlab
% 检查基础变量是否正确加载
load('example\Tar\basic\dem.tif')  % 使用适当的读取函数
imagesc(dem)  % 可视化检查
```

### 逐步测试
1. **测试数据读取**：
   ```matlab
   addpath('CREST_app')
   globalPar = GlobalParameters('配置文件路径')
   ```

2. **测试基础数据**：
   ```matlab
   basinVar = BasinVariables(globalPar.basicPath, globalPar.slopeMode)
   ```

3. **测试观测数据**：
   ```matlab
   hydroSites = HydroSites(globalPar.obsPath, globalPar.sitesShpFile, ...)
   ```

---

## 📞 获取帮助

### 自助诊断
1. 查看MATLAB命令窗口的完整错误信息
2. 检查result目录下的log.txt文件
3. 对比示例数据的文件结构和格式

### 问题报告格式
如需技术支持，请提供：
1. **错误信息**：完整的错误消息
2. **系统信息**：MATLAB版本、操作系统
3. **数据信息**：数据文件大小、格式
4. **配置文件**：相关的配置参数
5. **重现步骤**：导致错误的操作步骤

### 预防措施
1. **备份数据**：运行前备份重要数据
2. **小规模测试**：先用小数据集测试
3. **逐步增加复杂度**：从简单配置开始
4. **定期保存**：长时间运行时定期保存中间结果

---

## ✅ 成功运行检查清单

运行成功的标志：
- [ ] 控制台显示时间进度
- [ ] 显示性能指标 (NSCE, Bias, CC)
- [ ] 生成结果CSV文件
- [ ] 无错误信息输出
- [ ] 结果数值合理

**记住：大多数问题都是由数据格式或路径配置引起的，仔细检查这两个方面通常能解决90%的问题！** 🎯
