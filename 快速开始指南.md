# CREST 模型快速开始指南

## 🚀 5分钟快速上手

### 第一步：检查数据文件
确保以下文件存在：
```
example/Tar/
├── basic/
│   ├── dem.tif          ✓ 已准备
│   ├── fdr.tif          ✓ 已准备  
│   ├── fac.tif          ✓ 已准备
│   ├── stream.tif       ✓ 已准备
│   └── mask.tif         ✓ 已准备
├── Rains_daily/
│   └── rain.*.mat       ✓ 已准备
├── PETs_daily/  
│   └── pet_*.mat        ✓ 已准备
├── obs/
│   ├── tp.shp           ✓ 已准备
│   └── tp_obs.csv       ✓ 已修复
└── param/
    └── ParametersTest.txt ✓ 已准备
```

### 第二步：运行模拟
在MATLAB命令窗口中执行：
```matlab
cd('CREST_app')
CREST('D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\Tar_local_2.1.3_daily_win.Project', 'auto', 1)
```

### 第三步：查看结果
- **控制台输出**：显示NSCE、Bias、CC等性能指标
- **结果文件**：`example/Tar/result/110.68916600000.csv`

---

## 🔧 参数优化 (可选)

### 修改运行模式
编辑配置文件 `Tar_local_2.1.3_daily_win.Project`：
```ini
RunStyle = cali_SCEUA    # 改为优化模式
CalibMode = Sequential   # 使用串行优化 (稳定)
```

### 运行优化
```matlab
cd('CREST_app')
CREST('D:\CREST\CREST2.1.3_redist\MJ2.1.3\example\Tar\Tar_local_2.1.3_daily_win.Project', 'auto', 1)
```

**注意**：优化过程可能需要30分钟到数小时

---

## 📊 结果解读

### 性能指标含义
- **NSCE > 0.5**：模型性能可接受
- **NSCE > 0.7**：模型性能良好  
- **NSCE > 0.8**：模型性能优秀
- **CC > 0.8**：时间序列相关性很好

### 当前示例结果
```
NSCE = -0.45228  (需要优化)
Bias = -100      (存在系统偏差)
CC = 0.9691      (时间序列相关性很好)
```

---

## ⚠️ 常见错误及解决

### 错误1：文件路径问题
```
错误：找不到文件
解决：检查配置文件中的路径是否使用反斜杠 \
```

### 错误2：观测数据格式
```
错误：观测数据读取失败
解决：确保文件名为 站点ID_obs.csv 格式
```

### 错误3：内存不足
```
错误：优化过程中断
解决：减少 maxn 参数值，或使用Sequential模式
```

---

## 🎯 下一步建议

1. **熟悉基本操作**：先运行几次模拟模式
2. **理解参数含义**：阅读详细用户指南
3. **尝试参数优化**：提高模型性能
4. **应用自己数据**：准备自己的流域数据

**成功运行示例后，您就可以开始使用自己的数据了！** 🎉
