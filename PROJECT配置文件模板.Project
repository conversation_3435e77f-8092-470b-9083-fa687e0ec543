###############################################################################
# CREST Project File Template
# 请根据您的实际情况修改以下参数
###############################################################################
Version						=			2.1.3
###############################################################################
# 时间设置 - 根据您的数据时间范围修改
###############################################################################
TimeMark					=			h	# h=小时, d=日
TimeFormat					=			yyyymmddHH	# 时间格式
TimeStep					=			1	# 时间步长
StartDate					= 			2020081508	# 模拟开始时间
NLoad						=			0	# 状态加载次数，通常为0
WarmupDate					=			2020081911	# 暖启动结束时间
EndDate						= 			2020090205	# 模拟结束时间
###############################################################################
# 运行模式设置
###############################################################################
RunStyle					=			simu	# simu=模拟, cali_SCEUA=优化
Feedback					=			No	# 汇流反馈，通常为No
hasRiverInterflow			=			No	# 河道壤中流，通常为No
UseLAI						=			No	# 是否使用LAI数据
###############################################################################
# 数据路径设置 - 请修改为您的实际路径
###############################################################################
BasicFormat					=			.tif
BasicPath					=			"您的路径\basic\"

ParamFormat					=			.asc
ParamPath					=			"您的路径\param\ParametersTest.txt"

StateFormat					=			.asc
StatePath					=			"您的路径\states\"

ICSFormat					=			.asc
ICSPath						=			"您的路径\ICS\"
###############################################################################
# 降雨数据设置
###############################################################################
# 外部降雨数据（如果使用）
RainFormat					=			.24h.Z
RainDateFormat				=			yyyymmddHH
RainDateConv				=			End
RainStart					=			2020081508
RainDateInterval			=			0000000100
RainPathExt					=			"外部降雨数据路径前缀"
RainTsScaling				=			1

# 内部降雨数据（推荐使用）
RainPathInt					=			"您的路径\rains_daily\rain."
###############################################################################
# PET数据设置
###############################################################################
# 外部PET数据（如果使用）
PETFormat					=			.bil
PETDateFormat				=			yymmdd
PETDateConv					=			Begin
PETStart					=			020101
PETDateInterval				=			000001
PETPathExt					=			"外部PET数据路径前缀"
PETTsScaling				=			100

# 内部PET数据（推荐使用）
PETPathInt					=			"您的路径\PETs_daily\pet_"
###############################################################################
# LAI数据设置（仅当UseLAI=Yes时需要）
###############################################################################
LAIFormat					=			_mosaic.tif
LAIDateFormat				=			yyyy\DOY
LAIStart					=			2006\001
LAIDateInterval				=			0000\008
LAIPathExt					=			"LAI数据路径"
LAITsScaling				=			10
LAIDateConv					=			Center
LAIPathInt					=			"您的路径\LAI_daily\"
###############################################################################
# 结果输出设置
###############################################################################
ResultFormat				=			asc
ResultPath					=			"您的路径\result\"
###############################################################################
# 参数优化设置（仅当RunStyle=cali_SCEUA时需要）
###############################################################################
CalibFormat					=			asc
CalibPath					=			"您的路径\Calib\"
CalibMode					=			Sequential	# Sequential=串行, Parallel=并行
###############################################################################
# 观测数据设置
###############################################################################
OBSDateFormat				=			yyyymmddHH	# 观测数据日期格式
OBSPath						=			"您的路径\obs\"
OBSNoDataValue				=			-9999
###############################################################################
# 出水口设置
###############################################################################
HasOutlet					=			yes
OutletName					=			您的站点ID	# 必须与shapefile中的ID一致
SitesShpFile				=			您的站点.shp	# shapefile文件名
###############################################################################
# 栅格输出开关（建议全部设为No以节省空间）
###############################################################################
GOVar_Rain					=			No	# 降雨栅格输出
GOVar_PET					=			No	# PET栅格输出
GOVar_EPot					=			No	# 潜在蒸散发栅格输出
GOVar_EAct					=			No	# 实际蒸散发栅格输出
GOVar_W						=			No	# 土壤含水量栅格输出
GOVar_SM					=			No	# 土壤湿度栅格输出
GOVar_R						=			No	# 径流栅格输出
GOVar_ExcS					=			No	# 地表超渗栅格输出
GOVar_ExcI					=			No	# 壤中流栅格输出
GOVar_RS					=			No	# 地表径流栅格输出
GOVar_RI					=			No	# 壤中径流栅格输出
###############################################################################
# 特定时间输出设置（可选）
###############################################################################
NumOfOutputDates			=			0	# 指定输出时间点数量，0=不使用
SaveDateFormat				=			"yyyymmddHHMM"
DateOffset					=			000000000030
OutputDate_1				=			2020081911
OutputDate_2				=			2020082014
OutputDate_3				=			2020082520
OutputDate_4				=			2020090205
###############################################################################
# 系统设置（通常不需要修改）
###############################################################################
DecompBeforeSrc				=			""您的路径\MEX" -ibck x "
DecompBeforeDst 			=			" "
OS							=			"windows"

###############################################################################
# 配置说明：
# 1. 将所有"您的路径"替换为实际的数据路径
# 2. 将"您的站点ID"和"您的站点.shp"替换为实际的站点信息
# 3. 根据数据时间范围调整时间设置
# 4. 新手建议使用simu模式，熟悉后再尝试cali_SCEUA模式
# 5. 路径必须使用反斜杠(\)，包含空格的路径要用双引号包围
###############################################################################
