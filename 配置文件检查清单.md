# CREST PROJECT 配置文件检查清单

## ✅ 配置前准备

### 数据文件检查
- [ ] **基础地理数据** (basic目录)
  - [ ] dem.tif - 数字高程模型
  - [ ] fdr.tif - 流向数据
  - [ ] fac.tif - 汇流累积
  - [ ] stream.tif - 河网数据
  - [ ] mask.tif - 流域掩膜

- [ ] **气象强迫数据**
  - [ ] 降雨数据文件 (rain.yyyymmddHH.mat)
  - [ ] PET数据文件 (pet_yyyymmddHH.mat)
  - [ ] 数据时间范围覆盖模拟期间

- [ ] **观测数据**
  - [ ] 站点shapefile文件 (*.shp及相关文件)
  - [ ] 观测数据CSV文件 (站点ID_obs.csv)

- [ ] **模型参数**
  - [ ] ParametersTest.txt 参数文件

---

## ⚙️ 基本配置检查

### 时间设置
- [ ] **TimeMark** 与数据时间单位一致
  - 小时数据 → `h`
  - 日数据 → `d`

- [ ] **TimeFormat** 与数据文件命名格式一致
  - 小时数据 → `yyyymmddHH`
  - 日数据 → `yyyymmdd`

- [ ] **时间逻辑检查**
  - [ ] StartDate ≤ WarmupDate ≤ EndDate
  - [ ] WarmupDate 比 StartDate 晚几天（建议3-7天）
  - [ ] 时间范围在数据覆盖范围内

### 路径设置
- [ ] **路径格式正确**
  - [ ] 使用反斜杠 `\` (Windows)
  - [ ] 路径以反斜杠结尾
  - [ ] 包含空格的路径用双引号包围

- [ ] **路径存在性检查**
  - [ ] BasicPath 目录存在
  - [ ] RainPathInt 前缀路径存在
  - [ ] PETPathInt 前缀路径存在
  - [ ] OBSPath 目录存在
  - [ ] ParamPath 文件存在
  - [ ] ResultPath 目录存在（不存在会自动创建）

### 运行模式设置
- [ ] **RunStyle** 选择合适
  - 新手测试 → `simu`
  - 参数优化 → `cali_SCEUA`

- [ ] **CalibMode** 设置（仅优化模式）
  - 稳定运行 → `Sequential`
  - 快速运行 → `Parallel` (需要工具箱)

---

## 🎯 观测数据配置检查

### 站点信息
- [ ] **OutletName** 与shapefile中的站点ID完全一致
- [ ] **SitesShpFile** 文件名正确
- [ ] **观测文件命名** 格式为 `站点ID_obs.csv`

### 观测数据格式
- [ ] **OBSDateFormat** 与观测数据文件中的日期格式一致
- [ ] **观测数据内容** 包含Date和Flow两列
- [ ] **时间范围** 覆盖模拟期间（至少WarmupDate到EndDate）

---

## 🔧 高级配置检查

### 强迫数据设置
- [ ] **内部数据路径** (推荐)
  - [ ] RainPathInt 设置正确
  - [ ] PETPathInt 设置正确
  - [ ] 文件命名格式匹配

- [ ] **外部数据路径** (可选)
  - [ ] RainPathExt 设置正确
  - [ ] PETPathExt 设置正确
  - [ ] 日期格式和间隔设置正确

### 输出设置
- [ ] **栅格输出开关** 根据需要设置
  - 大流域建议全部设为 `No`
  - 小流域可选择性开启

- [ ] **特定时间输出** (可选)
  - [ ] NumOfOutputDates 设置
  - [ ] OutputDate_X 时间点设置

---

## ⚠️ 常见配置错误

### 路径问题
❌ **错误示例**：
```ini
BasicPath = "D:/CREST/basic"          # 使用了正斜杠
BasicPath = "D:\CREST\basic"          # 缺少结尾反斜杠
BasicPath = D:\CREST\basic\           # 缺少双引号
```

✅ **正确示例**：
```ini
BasicPath = "D:\CREST\basic\"
```

### 时间格式问题
❌ **错误示例**：
```ini
TimeFormat = yyyymmddHH
OBSDateFormat = yyyymmdd              # 格式不一致
```

✅ **正确示例**：
```ini
TimeFormat = yyyymmddHH
OBSDateFormat = yyyymmddHH            # 格式一致
```

### 站点ID问题
❌ **错误示例**：
```ini
OutletName = tp                       # 使用站点名称
```

✅ **正确示例**：
```ini
OutletName = 110.68916600000          # 使用shapefile中的实际ID
```

---

## 🚀 配置完成后的测试

### 基本测试
1. **语法检查**：确保配置文件无语法错误
2. **路径测试**：在MATLAB中测试关键路径是否可访问
3. **数据加载测试**：尝试加载一个数据文件

### 运行测试
```matlab
% 在MATLAB中运行
cd('CREST_app')
CREST('您的配置文件.Project', 'auto', 1)
```

### 成功标志
- [ ] 无错误信息
- [ ] 显示时间进度
- [ ] 生成结果文件
- [ ] 显示性能指标

---

## 📋 配置文件模板使用

1. **复制模板**：使用提供的 `PROJECT配置文件模板.Project`
2. **批量替换**：将所有"您的路径"替换为实际路径
3. **逐项检查**：按照本清单逐项验证
4. **测试运行**：先用小时间窗口测试
5. **正式运行**：确认无误后进行完整模拟

---

## 🎯 最佳实践建议

1. **备份原始配置**：修改前备份工作配置
2. **渐进式配置**：先配置基本功能，再添加高级选项
3. **小规模测试**：用短时间窗口测试配置
4. **文档记录**：记录重要的配置修改
5. **版本管理**：为不同试验保存不同版本的配置

**配置成功的关键是细心和耐心！** ✨
