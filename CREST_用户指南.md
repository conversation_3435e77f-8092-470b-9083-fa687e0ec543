# CREST 水文模型用户指南

## 📋 目录
1. [模型简介](#模型简介)
2. [系统要求](#系统要求)
3. [数据准备](#数据准备)
4. [配置文件设置](#配置文件设置)
5. [运行模式](#运行模式)
6. [结果分析](#结果分析)
7. [常见问题](#常见问题)

---

## 🌊 模型简介

**CREST (Coupled Routing and Excess Storage)** 是一个分布式水文模型，用于流域水文过程模拟和预报。

### 主要特点
- **分布式建模**：基于栅格的空间分布式计算
- **水文过程完整**：包含产流、汇流、蒸散发等完整水循环
- **参数优化**：支持SCE-UA自动参数优化算法
- **多时间尺度**：支持小时、日等不同时间步长

### 版本信息
- **版本**：v2.1.3 (2015年7月)
- **开发单位**：俄克拉荷马大学 & NASA
- **适用平台**：Windows x64 + MATLAB

---

## 💻 系统要求

### 必需软件
- **MATLAB** R2014a 或更高版本
- **Windows** x64 操作系统
- **内存**：建议8GB以上
- **硬盘空间**：至少2GB可用空间

### 可选组件
- **Parallel Computing Toolbox**：用于参数优化加速
- **Statistics and Machine Learning Toolbox**：用于统计分析

---

## 📁 数据准备

### 3.1 基础地理数据 (basic目录)
所有文件必须为GeoTIFF格式(.tif)，投影坐标系统一致：

| 文件名 | 描述 | 要求 |
|--------|------|------|
| `dem.tif` | 数字高程模型 | 必需，单位：米 |
| `fdr.tif` | 流向数据 | 必需，D8格式 |
| `fac.tif` | 汇流累积 | 必需 |
| `stream.tif` | 河网数据 | 必需，1=河道，0=非河道 |
| `mask.tif` | 流域掩膜 | 必需，1=流域内，0=流域外 |

### 3.2 气象强迫数据
#### 降雨数据 (Rains_daily目录)
- **文件格式**：`rain.yyyymmddHH.mat`
- **变量名**：`data` (二维矩阵，与DEM同尺寸)
- **单位**：mm/h
- **示例**：`rain.2020081911.mat`

#### 蒸散发数据 (PETs_daily目录)  
- **文件格式**：`pet_yyyymmddHH.mat`
- **变量名**：`data` (二维矩阵，与DEM同尺寸)
- **单位**：mm/h
- **示例**：`pet_2020081911.mat`

### 3.3 观测数据 (obs目录)
#### 水文站点Shapefile
- **文件**：`站点名.shp` (如tp.shp, sy.shp, dh.shp)
- **属性**：包含站点坐标信息
- **坐标系**：与基础地理数据一致

#### 流量观测数据
- **文件格式**：`站点ID_obs.csv`
- **内容格式**：
```csv
Date,Flow
2020081911,10.5
2020081912,12.3
```
- **日期格式**：yyyymmddHH (小时数据) 或 yyyymmdd (日数据)
- **流量单位**：m³/s

### 3.4 模型参数 (param目录)
- **文件**：`ParametersTest.txt`
- **内容**：11个水文参数的空间分布
- **格式**：文本文件，每行对应一个参数

---

## ⚙️ 配置文件设置

### 4.1 主配置文件 (.Project文件)

关键参数设置：

```ini
# 基本设置
RunStyle = simu                    # 运行模式：simu(模拟) / cali_SCEUA(优化)
TimeMark = h                       # 时间单位：h(小时) / d(日)
TimeStep = 1                       # 时间步长

# 时间设置  
WarmupDate = 2020081911           # 暖启动时间 (yyyymmddHH)
EndDate = 2020090205              # 结束时间 (yyyymmddHH)

# 路径设置
BasicPath = "路径\basic\"          # 基础数据路径
RainPath = "路径\Rains_daily\"     # 降雨数据路径  
PETPath = "路径\PETs_daily\"       # PET数据路径
OBSPath = "路径\obs\"              # 观测数据路径
ParamPath = "路径\param\"          # 参数文件路径
ResPath = "路径\result\"           # 结果输出路径

# 观测站点设置
OutletName = 站点ID               # 出水口站点ID
SitesShpFile = 站点名.shp         # 站点shapefile文件名
OBSDateFormat = yyyymmddHH        # 观测数据日期格式

# 优化设置 (仅优化模式需要)
CalibMode = Parallel              # 优化模式：Parallel(并行) / Sequential(串行)
CalibPath = "路径\Calib\"         # 优化配置路径
```

### 4.2 优化配置文件 (Calib/Calibrations.txt)

```ini
# SCE-UA算法参数
maxn = 5000                       # 最大迭代次数
kstop = 5                         # 收敛判断循环数
pcento = 0.01                     # 收敛阈值
peps = 0.001                      # 参数收敛精度
ngs = 5                           # 复合体数量

# 目标站点
Name_1 = 站点ID                   # 优化目标站点ID

# 参数范围 (示例)
WM_1 = 50.0    100.0    200.0     # 最小值 初始值 最大值
B_1 = 0.1      0.5      2.0
IM_1 = 0.0     0.1      0.5
```

---

## 🚀 运行模式

### 5.1 模拟模式 (推荐新手)

**用途**：使用给定参数进行水文模拟

**步骤**：
1. 确保所有数据文件准备完整
2. 设置配置文件：`RunStyle = simu`
3. 在MATLAB中运行：
```matlab
cd('CREST_app')
CREST('完整路径\项目配置文件.Project', 'auto', 1)
```

**输出**：
- 控制台显示模拟进度和性能指标
- 结果文件保存在ResPath指定目录

### 5.2 参数优化模式 (高级用户)

**用途**：自动优化模型参数以提高模拟精度

**步骤**：
1. 完成模拟模式测试，确保模型可正常运行
2. 准备优化配置文件 (Calib/Calibrations.txt)
3. 设置配置文件：`RunStyle = cali_SCEUA`
4. 选择优化模式：
   - `CalibMode = Sequential`：串行优化 (稳定，较慢)
   - `CalibMode = Parallel`：并行优化 (快速，需要Parallel Computing Toolbox)

**运行命令**：
```matlab
cd('CREST_app')
CREST('完整路径\项目配置文件.Project', 'auto', 1)
```

**注意事项**：
- 优化过程可能需要数小时至数天
- 建议先用较小的maxn值测试
- 并行模式需要足够的内存和CPU核心

---

## 📊 结果分析

### 6.1 模型性能指标

**Nash-Sutcliffe效率系数 (NSCE)**：
- 范围：-∞ to 1
- NSCE > 0.5：模型性能良好
- NSCE > 0.7：模型性能很好
- NSCE > 0.8：模型性能优秀

**偏差 (Bias)**：
- 单位：%
- Bias接近0表示模拟值与观测值总量匹配良好

**相关系数 (CC)**：
- 范围：-1 to 1  
- CC > 0.8：模拟与观测时间序列相关性很好

### 6.2 输出文件

**结果目录 (result)**：
- `站点ID.csv`：详细的水文过程时间序列
- `log.txt`：优化过程日志 (仅优化模式)
- `SCE_UA_yyyymmdd_hh.txt`：最优参数 (仅优化模式)

**CSV文件内容**：
| 列名 | 描述 | 单位 |
|------|------|------|
| Date | 日期时间 | - |
| rainAct | 实际降雨 | mm |
| rain | 有效降雨 | mm |
| PET | 潜在蒸散发 | mm |
| Eact | 实际蒸散发 | mm |
| W | 土壤含水量 | mm |
| SM | 土壤湿度 | % |
| R | 模拟径流 | m³/s |
| R_Obs | 观测径流 | m³/s |

---

## ❓ 常见问题

### Q1: 模型运行时提示"文件不存在"
**解决方案**：
- 检查配置文件中的路径设置是否正确
- 确保所有数据文件存在且文件名格式正确
- 使用绝对路径避免相对路径问题

### Q2: 观测数据读取失败
**解决方案**：
- 确保观测数据文件名为：`站点ID_obs.csv`
- 检查站点ID是否与shapefile中的属性匹配
- 验证日期格式是否与OBSDateFormat设置一致

### Q3: 并行优化卡住不动
**解决方案**：
- 检查是否安装了Parallel Computing Toolbox
- 尝试使用Sequential模式
- 减少ngs参数值 (复合体数量)
- 检查内存是否充足

### Q4: 模型性能指标很差 (NSCE < 0)
**解决方案**：
- 检查观测数据质量和时间对应关系
- 验证气象强迫数据的合理性
- 考虑进行参数优化
- 检查流域边界和河网提取是否正确

### Q5: 优化过程中断
**解决方案**：
- 减少maxn值进行测试
- 检查磁盘空间是否充足
- 确保MATLAB版本兼容性
- 尝试重启MATLAB清理内存

---

## 📞 技术支持

如遇到技术问题，请检查：
1. 数据文件完整性和格式
2. 配置文件参数设置
3. MATLAB版本和工具箱
4. 系统资源 (内存、磁盘空间)

**祝您使用愉快！** 🎉
